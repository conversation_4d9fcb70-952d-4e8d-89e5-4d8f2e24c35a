using System;
using System.Drawing;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容替换设置窗体辅助方法
    /// </summary>
    public partial class ContentReplacementForm
    {
        #region UI创建辅助方法

        /// <summary>
        /// 创建可滚动的面板
        /// </summary>
        private static Panel CreateScrollablePanel()
        {
            return new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };
        }

        /// <summary>
        /// 创建复选框
        /// </summary>
        private static CheckBox CreateCheckBox(string text, int x, int y)
        {
            return new CheckBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(450, 45),
                UseVisualStyleBackColor = true,
                AutoSize = false,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                TextAlign = ContentAlignment.MiddleLeft
            };
        }

        /// <summary>
        /// 创建分组框
        /// </summary>
        private static GroupBox CreateGroupBox(string text, int x, int y, int width, int height)
        {
            return new GroupBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                Padding = new Padding(20, 25, 20, 20)
            };
        }

        /// <summary>
        /// 创建按钮
        /// </summary>
        private static Button CreateButton(string text, int x, int y, int width = 100, int height = 45)
        {
            return new Button
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };
        }

        /// <summary>
        /// 创建标签
        /// </summary>
        private static Label CreateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(140, 45),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };
        }

        /// <summary>
        /// 创建下拉框
        /// </summary>
        private static ComboBox CreateComboBox(int x, int y, int width)
        {
            var comboBox = new ComboBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 35),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };

            // 设置下拉框文本居中显示
            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.ItemHeight = 35;
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = comboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    // 修复编译警告：确保字体不为null
                    var font = e.Font ?? comboBox.Font ?? SystemFonts.DefaultFont;
                    TextRenderer.DrawText(e.Graphics, text, font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            return comboBox;
        }

        /// <summary>
        /// 创建文本框 - 单行输入框居中对齐，多行文本框左对齐
        /// </summary>
        private static TextBox CreateTextBox(int x, int y, int width, bool centerAlign = true)
        {
            return new TextBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 35),
                TextAlign = centerAlign ? HorizontalAlignment.Center : HorizontalAlignment.Left,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };
        }

        /// <summary>
        /// 创建多行文本框 - 多行文本框始终左对齐
        /// </summary>
        private static TextBox CreateMultilineTextBox(int x, int y, int width, int height)
        {
            return new TextBox
            {
                Location = new Point(x, y),
                Size = new Size(width, height),
                TextAlign = HorizontalAlignment.Left, // 多行文本框左对齐
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                AcceptsReturn = true,
                AcceptsTab = true
            };
        }



        #endregion

        #region 事件处理

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // 确定按钮点击事件
                btnOK.Click += BtnOK_Click;

                // 取消按钮点击事件
                btnCancel.Click += BtnCancel_Click;

                // 设置规则管理按钮事件
                SetupRuleManagementEvents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置事件处理程序失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置规则管理事件
        /// </summary>
        private void SetupRuleManagementEvents()
        {
            // 文本替换规则管理
            SetupTextReplacementRuleEvents();

            // 形状替换规则管理
            SetupShapeReplacementRuleEvents();

            // 字体替换规则管理
            SetupFontReplacementRuleEvents();

            // 颜色替换规则管理
            SetupColorReplacementRuleEvents();
        }

        /// <summary>
        /// 设置文本替换规则事件
        /// </summary>
        private void SetupTextReplacementRuleEvents()
        {
            // 规则列表管理按钮
            var btnAdd = FindControlInPanel<Button>("TextReplacement", "btnAddTextReplacementRule");
            if (btnAdd != null)
                btnAdd.Click += BtnAddTextReplacementRule_Click;

            var btnEdit = FindControlInPanel<Button>("TextReplacement", "btnEditTextReplacementRule");
            if (btnEdit != null)
                btnEdit.Click += BtnEditTextReplacementRule_Click;

            var btnDelete = FindControlInPanel<Button>("TextReplacement", "btnDeleteTextReplacementRule");
            if (btnDelete != null)
                btnDelete.Click += BtnDeleteTextReplacementRule_Click;

            var btnImport = FindControlInPanel<Button>("TextReplacement", "btnImportTextReplacementRules");
            if (btnImport != null)
                btnImport.Click += BtnImportTextReplacementRules_Click;

            var btnExport = FindControlInPanel<Button>("TextReplacement", "btnExportTextReplacementRules");
            if (btnExport != null)
                btnExport.Click += BtnExportTextReplacementRules_Click;

            var btnDownloadTemplate = FindControlInPanel<Button>("TextReplacement", "btnDownloadExcelTemplate");
            if (btnDownloadTemplate != null)
                btnDownloadTemplate.Click += BtnDownloadExcelTemplate_Click;

            // 规则编辑按钮
            var btnNewTextRule = FindControlInPanel<Button>("TextReplacement", "btnNewTextRuleEdit");
            if (btnNewTextRule != null)
                btnNewTextRule.Click += BtnNewTextRule_Click;

            var btnSaveTextRule = FindControlInPanel<Button>("TextReplacement", "btnSaveTextRule");
            if (btnSaveTextRule != null)
                btnSaveTextRule.Click += BtnSaveTextRule_Click;

            var btnClearTextRule = FindControlInPanel<Button>("TextReplacement", "btnClearTextRule");
            if (btnClearTextRule != null)
                btnClearTextRule.Click += BtnClearTextRule_Click;
        }

        /// <summary>
        /// 设置形状替换规则事件
        /// </summary>
        private void SetupShapeReplacementRuleEvents()
        {
            // 图片替换规则
            var btnAddImage = FindControlInPanel<Button>("ShapeReplacement", "btnAddImageReplacementRule");
            if (btnAddImage != null)
                btnAddImage.Click += BtnAddImageReplacementRule_Click;

            var btnEditImage = FindControlInPanel<Button>("ShapeReplacement", "btnEditImageReplacementRule");
            if (btnEditImage != null)
                btnEditImage.Click += BtnEditImageReplacementRule_Click;

            var btnDeleteImage = FindControlInPanel<Button>("ShapeReplacement", "btnDeleteImageReplacementRule");
            if (btnDeleteImage != null)
                btnDeleteImage.Click += BtnDeleteImageReplacementRule_Click;

            // 文本框替换规则
            var btnAddTextBox = FindControlInPanel<Button>("ShapeReplacement", "btnAddTextBoxReplacementRule");
            if (btnAddTextBox != null)
                btnAddTextBox.Click += BtnAddTextBoxReplacementRule_Click;

            var btnEditTextBox = FindControlInPanel<Button>("ShapeReplacement", "btnEditTextBoxReplacementRule");
            if (btnEditTextBox != null)
                btnEditTextBox.Click += BtnEditTextBoxReplacementRule_Click;

            var btnDeleteTextBox = FindControlInPanel<Button>("ShapeReplacement", "btnDeleteTextBoxReplacementRule");
            if (btnDeleteTextBox != null)
                btnDeleteTextBox.Click += BtnDeleteTextBoxReplacementRule_Click;

            // 形状样式替换规则
            var btnAddShapeStyle = FindControlInPanel<Button>("ShapeReplacement", "btnAddShapeStyleReplacementRule");
            if (btnAddShapeStyle != null)
                btnAddShapeStyle.Click += BtnAddShapeStyleReplacementRule_Click;

            var btnEditShapeStyle = FindControlInPanel<Button>("ShapeReplacement", "btnEditShapeStyleReplacementRule");
            if (btnEditShapeStyle != null)
                btnEditShapeStyle.Click += BtnEditShapeStyleReplacementRule_Click;

            var btnDeleteShapeStyle = FindControlInPanel<Button>("ShapeReplacement", "btnDeleteShapeStyleReplacementRule");
            if (btnDeleteShapeStyle != null)
                btnDeleteShapeStyle.Click += BtnDeleteShapeStyleReplacementRule_Click;

            // 图片替换规则编辑按钮
            var btnNewImageRule = FindControlInPanel<Button>("ShapeReplacement", "btnNewImageRuleEdit");
            if (btnNewImageRule != null)
                btnNewImageRule.Click += BtnNewImageRule_Click;

            var btnSaveImageRule = FindControlInPanel<Button>("ShapeReplacement", "btnSaveImageRule");
            if (btnSaveImageRule != null)
                btnSaveImageRule.Click += BtnSaveImageRule_Click;

            var btnClearImageRule = FindControlInPanel<Button>("ShapeReplacement", "btnClearImageRule");
            if (btnClearImageRule != null)
                btnClearImageRule.Click += BtnClearImageRule_Click;

            // 文本框替换规则编辑按钮
            var btnNewTextBoxRule = FindControlInPanel<Button>("ShapeReplacement", "btnNewTextBoxRuleEdit");
            if (btnNewTextBoxRule != null)
                btnNewTextBoxRule.Click += BtnNewTextBoxRule_Click;

            var btnSaveTextBoxRule = FindControlInPanel<Button>("ShapeReplacement", "btnSaveTextBoxRule");
            if (btnSaveTextBoxRule != null)
                btnSaveTextBoxRule.Click += BtnSaveTextBoxRule_Click;

            var btnClearTextBoxRule = FindControlInPanel<Button>("ShapeReplacement", "btnClearTextBoxRule");
            if (btnClearTextBoxRule != null)
                btnClearTextBoxRule.Click += BtnClearTextBoxRule_Click;

            // 形状样式替换规则编辑按钮
            var btnNewShapeStyleRule = FindControlInPanel<Button>("ShapeReplacement", "btnNewShapeStyleRuleEdit");
            if (btnNewShapeStyleRule != null)
                btnNewShapeStyleRule.Click += BtnNewShapeStyleRule_Click;

            var btnSaveShapeStyleRule = FindControlInPanel<Button>("ShapeReplacement", "btnSaveShapeStyleRule");
            if (btnSaveShapeStyleRule != null)
                btnSaveShapeStyleRule.Click += BtnSaveShapeStyleRule_Click;

            var btnClearShapeStyleRule = FindControlInPanel<Button>("ShapeReplacement", "btnClearShapeStyleRule");
            if (btnClearShapeStyleRule != null)
                btnClearShapeStyleRule.Click += BtnClearShapeStyleRule_Click;
        }

        /// <summary>
        /// 设置字体替换规则事件
        /// </summary>
        private void SetupFontReplacementRuleEvents()
        {
            // 字体名称替换规则
            var btnAddFontName = FindControlInPanel<Button>("FontReplacement", "btnAddFontNameReplacementRule");
            if (btnAddFontName != null)
                btnAddFontName.Click += BtnAddFontNameReplacementRule_Click;

            var btnEditFontName = FindControlInPanel<Button>("FontReplacement", "btnEditFontNameReplacementRule");
            if (btnEditFontName != null)
                btnEditFontName.Click += BtnEditFontNameReplacementRule_Click;

            var btnDeleteFontName = FindControlInPanel<Button>("FontReplacement", "btnDeleteFontNameReplacementRule");
            if (btnDeleteFontName != null)
                btnDeleteFontName.Click += BtnDeleteFontNameReplacementRule_Click;

            // 字体样式替换规则
            var btnAddFontStyle = FindControlInPanel<Button>("FontReplacement", "btnAddFontStyleReplacementRule");
            if (btnAddFontStyle != null)
                btnAddFontStyle.Click += BtnAddFontStyleReplacementRule_Click;

            var btnEditFontStyle = FindControlInPanel<Button>("FontReplacement", "btnEditFontStyleReplacementRule");
            if (btnEditFontStyle != null)
                btnEditFontStyle.Click += BtnEditFontStyleReplacementRule_Click;

            var btnDeleteFontStyle = FindControlInPanel<Button>("FontReplacement", "btnDeleteFontStyleReplacementRule");
            if (btnDeleteFontStyle != null)
                btnDeleteFontStyle.Click += BtnDeleteFontStyleReplacementRule_Click;

            // 字体嵌入管理
            var btnAddFont = FindControlInPanel<Button>("FontReplacement", "btnAddFontToEmbed");
            if (btnAddFont != null)
                btnAddFont.Click += BtnAddFontToEmbed_Click;

            var btnRemoveFont = FindControlInPanel<Button>("FontReplacement", "btnRemoveFontToEmbed");
            if (btnRemoveFont != null)
                btnRemoveFont.Click += BtnRemoveFontToEmbed_Click;

            // 字体名称替换规则编辑按钮
            var btnNewFontNameRule = FindControlInPanel<Button>("FontReplacement", "btnNewFontNameRuleEdit");
            if (btnNewFontNameRule != null)
                btnNewFontNameRule.Click += BtnNewFontNameRule_Click;

            var btnSaveFontNameRule = FindControlInPanel<Button>("FontReplacement", "btnSaveFontNameRule");
            if (btnSaveFontNameRule != null)
                btnSaveFontNameRule.Click += BtnSaveFontNameRule_Click;

            var btnClearFontNameRule = FindControlInPanel<Button>("FontReplacement", "btnClearFontNameRule");
            if (btnClearFontNameRule != null)
                btnClearFontNameRule.Click += BtnClearFontNameRule_Click;

            // 字体样式替换规则编辑按钮
            var btnNewFontStyleRule = FindControlInPanel<Button>("FontReplacement", "btnNewFontStyleRuleEdit");
            if (btnNewFontStyleRule != null)
                btnNewFontStyleRule.Click += BtnNewFontStyleRule_Click;

            var btnSaveFontStyleRule = FindControlInPanel<Button>("FontReplacement", "btnSaveFontStyleRule");
            if (btnSaveFontStyleRule != null)
                btnSaveFontStyleRule.Click += BtnSaveFontStyleRule_Click;

            var btnClearFontStyleRule = FindControlInPanel<Button>("FontReplacement", "btnClearFontStyleRule");
            if (btnClearFontStyleRule != null)
                btnClearFontStyleRule.Click += BtnClearFontStyleRule_Click;
        }

        /// <summary>
        /// 设置颜色替换规则事件
        /// </summary>
        private void SetupColorReplacementRuleEvents()
        {
            // 主题颜色替换规则
            var btnAddThemeColor = FindControlInPanel<Button>("ColorReplacement", "btnAddThemeColorReplacementRule");
            if (btnAddThemeColor != null)
                btnAddThemeColor.Click += BtnAddThemeColorReplacementRule_Click;

            var btnEditThemeColor = FindControlInPanel<Button>("ColorReplacement", "btnEditThemeColorReplacementRule");
            if (btnEditThemeColor != null)
                btnEditThemeColor.Click += BtnEditThemeColorReplacementRule_Click;

            var btnDeleteThemeColor = FindControlInPanel<Button>("ColorReplacement", "btnDeleteThemeColorReplacementRule");
            if (btnDeleteThemeColor != null)
                btnDeleteThemeColor.Click += BtnDeleteThemeColorReplacementRule_Click;

            // 自定义颜色替换规则
            var btnAddCustomColor = FindControlInPanel<Button>("ColorReplacement", "btnAddCustomColorReplacementRule");
            if (btnAddCustomColor != null)
                btnAddCustomColor.Click += BtnAddCustomColorReplacementRule_Click;

            var btnEditCustomColor = FindControlInPanel<Button>("ColorReplacement", "btnEditCustomColorReplacementRule");
            if (btnEditCustomColor != null)
                btnEditCustomColor.Click += BtnEditCustomColorReplacementRule_Click;

            var btnDeleteCustomColor = FindControlInPanel<Button>("ColorReplacement", "btnDeleteCustomColorReplacementRule");
            if (btnDeleteCustomColor != null)
                btnDeleteCustomColor.Click += BtnDeleteCustomColorReplacementRule_Click;

            // 颜色选择按钮
            var btnSourceColor = FindControlInPanel<Button>("ColorReplacement", "btnSourceColor");
            if (btnSourceColor != null)
                btnSourceColor.Click += BtnSourceColor_Click;

            var btnTargetColor = FindControlInPanel<Button>("ColorReplacement", "btnTargetColor");
            if (btnTargetColor != null)
                btnTargetColor.Click += BtnTargetColor_Click;

            // 颜色规则编辑按钮
            var btnNewColorRule = FindControlInPanel<Button>("ColorReplacement", "btnNewColorRuleEdit");
            if (btnNewColorRule != null)
                btnNewColorRule.Click += BtnNewColorRule_Click;

            var btnSaveColorRule = FindControlInPanel<Button>("ColorReplacement", "btnSaveColorRule");
            if (btnSaveColorRule != null)
                btnSaveColorRule.Click += BtnSaveColorRule_Click;

            var btnClearColorRule = FindControlInPanel<Button>("ColorReplacement", "btnClearColorRule");
            if (btnClearColorRule != null)
                btnClearColorRule.Click += BtnClearColorRule_Click;
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveUIToSettings();
                SaveSettingsToConfig();
                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }



        #endregion

        #region 设置加载和保存

        /// <summary>
        /// 加载设置到界面
        /// </summary>
        private void LoadSettingsToUI()
        {
            try
            {
                // 加载文本替换设置
                LoadTextReplacementSettings();

                // 加载形状替换设置
                LoadShapeReplacementSettings();

                // 加载字体替换设置
                LoadFontReplacementSettings();

                // 加载颜色替换设置
                LoadColorReplacementSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置到界面失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存界面设置到配置
        /// </summary>
        private void SaveUIToSettings()
        {
            try
            {
                // 保存文本替换设置
                SaveTextReplacementSettings();

                // 保存形状替换设置
                SaveShapeReplacementSettings();

                // 保存字体替换设置
                SaveFontReplacementSettings();

                // 保存颜色替换设置
                SaveColorReplacementSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存界面设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存设置到配置文件
        /// </summary>
        private void SaveSettingsToConfig()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                config.ContentReplacementSettings = _currentSettings;
                ConfigService.Instance.UpdateConfig(config);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置文件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 查找控件辅助方法

        /// <summary>
        /// 在指定面板中查找控件
        /// </summary>
        private T? FindControlInPanel<T>(string panelName, string controlName) where T : Control
        {
            if (_tabPanels.TryGetValue(panelName, out Panel? panel))
            {
                var result = FindControlRecursive<T>(panel, controlName);
                // 调试信息：如果找不到控件，输出调试信息
                if (result == null)
                {
                    System.Diagnostics.Debug.WriteLine($"未找到控件: Panel={panelName}, Control={controlName}, Type={typeof(T).Name}");
                }
                return result;
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"未找到面板: {panelName}");
            }
            return null;
        }

        /// <summary>
        /// 递归查找控件
        /// </summary>
        private T? FindControlRecursive<T>(Control parent, string controlName) where T : Control
        {
            foreach (Control control in parent.Controls)
            {
                if (control.Name == controlName && control is T result)
                {
                    return result;
                }

                var found = FindControlRecursive<T>(control, controlName);
                if (found != null)
                {
                    return found;
                }
            }
            return null;
        }

        #endregion
    }
}
